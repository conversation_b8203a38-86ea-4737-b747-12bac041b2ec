package main

import (
	"fmt"

	"time"
)

// 4、用协程顺序打印字符切片。例子：20, 30, 50, 40, 10。

func main() {

	arr := []int{20, 30, 40, 50, 10}

	i := 0

	ch := make(chan int, 5)
	go func() {

		for {

			write(ch, arr[i%5])

			i++

		}

	}()

	for i := 0; i < 1000; i++ {

		go read(ch)

	}


	time.Sleep(20 * time.Second)


}

func write(ch chan int, num int) {

	ch <- num

}

func read(ch chan int) {

	for {

		select {

		case k, _ := <-ch:

			fmt.Println("k:", k)

			time.Sleep(time.Second)

		}

	}

}
